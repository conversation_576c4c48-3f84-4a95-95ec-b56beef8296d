package com.zjxy.gisimportservice.service.Impl;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.ValidationResult;
import com.zjxy.gisimportservice.service.GisImportTaskService;
import com.zjxy.gisimportservice.service.GisManageTemplateService;
import com.zjxy.gisimportservice.service.ShapefileReaderService;
import com.zjxy.gisimportservice.service.TemplateBasedShapefileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 基于模板的Shapefile处理服务实现类
 */
@Slf4j
@Service
@DS("slave")
public class TemplateBasedShapefileServiceImpl implements TemplateBasedShapefileService {

    @Autowired
    private GisManageTemplateService templateService;

    @Autowired
    private ShapefileReaderService shapefileReader;

    @Autowired
    private GisImportTaskService gisImportTaskService;

    @Value("${gis.import.task.upload.temp-path:/temp/gis-uploads}")
    private String tempPath;


    @Override
    public Map<String, Object> processShapefileWithTemplate(InputStream zipInputStream, String fileName, Integer templateId) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始使用模板处理Shapefile，模板ID: {}, 文件名: {}", templateId, fileName);

            // 获取模板配置
            GisManageTemplate template = templateService.getTemplateById(templateId);
            if (template == null) {
                result.put("success", false);
                result.put("message", "未找到指定的模板，模板ID: " + templateId);
                return result;
            }


            // 使用模板化的Shapefile处理器处理文件
            int featuresProcessed;
            featuresProcessed = shapefileReader.processShapefileZipWithTemplate(zipInputStream, fileName, template);


            // 检查处理结果
            boolean success = featuresProcessed > 0;
            String message = success ?
                "使用模板处理Shapefile成功" :
                "Shapefile处理失败，没有成功导入任何记录";

            // 生成处理报告
            Map<String, Object> report = generateProcessingReport(featuresProcessed, 0, template);

            result.put("success", success);
            result.put("message", message);
            result.put("templateId", templateId);
            result.put("templateName", template.getNameZh());
            result.put("featuresProcessed", featuresProcessed);
            result.put("report", report);

            if (success) {
                log.info("使用模板处理Shapefile完成，处理了 {} 条记录", featuresProcessed);
            } else {
                log.error("使用模板处理Shapefile失败，没有成功导入任何记录");
            }

        } catch (Exception e) {
            log.error("使用模板处理Shapefile失败", e);
            result.put("success", false);
            result.put("message", "处理失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> processShapefileWithTemplateFromPath(String zipFilePath, Integer templateId) {
        DynamicDataSourceManager.build().useDataSource("slave");
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("从路径使用模板处理Shapefile，模板ID: {}, 文件路径: {}", templateId, zipFilePath);

            // 获取模板配置
            GisManageTemplate template = templateService.getTemplateById(templateId);
            if (template == null) {
                result.put("success", false);
                result.put("message", "未找到指定的模板，模板ID: " + templateId);
                return result;
            }


            // 使用模板化的Shapefile处理器处理文件
            int featuresProcessed = 0;
            if (shapefileReader instanceof ShapefileReaderServiceImpl) {
                // 对于路径处理，需要先读取文件再调用模板方法
                try (FileInputStream fis = new FileInputStream(zipFilePath)) {
                    featuresProcessed = shapefileReader
                        .processShapefileZipWithTemplate(fis, new File(zipFilePath).getName(), template);
                }
            }

            // 检查处理结果
            boolean success = featuresProcessed > 0;
            String message = success ?
                "使用模板处理Shapefile成功" :
                "Shapefile处理失败，没有成功导入任何记录";

            // 生成处理报告
            Map<String, Object> report = generateProcessingReport(featuresProcessed, 0, template);

            result.put("success", success);
            result.put("message", message);
            result.put("templateId", templateId);
            result.put("templateName", template.getNameZh());
            result.put("featuresProcessed", featuresProcessed);
            result.put("report", report);

            if (success) {
                log.info("从路径使用模板处理Shapefile完成，处理了 {} 条记录", featuresProcessed);
            } else {
                log.error("从路径使用模板处理Shapefile失败，没有成功导入任何记录");
            }

        } catch (Exception e) {
            log.error("从路径使用模板处理Shapefile失败", e);
            result.put("success", false);
            result.put("message", "处理失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> generateProcessingReport(int processedCount, int errorCount, GisManageTemplate template) {
        Map<String, Object> report = new HashMap<>();

        report.put("templateId", template.getId());
        report.put("templateName", template.getNameZh());
        report.put("processedCount", processedCount);
        report.put("errorCount", errorCount);
        report.put("successRate", errorCount == 0 ? 100.0 : (double) processedCount / (processedCount + errorCount) * 100);
        report.put("processTime", new Date());

        return report;
    }

    // ==================== 任务驱动模式新增方法实现 ====================

    @Override
    public String saveUploadedFile(MultipartFile file, String taskId) throws IOException {
        try {
            log.info("保存上传的Shapefile文件 - 任务ID: {}, 文件名: {}", taskId, file.getOriginalFilename());

            // 创建文件保存目录
            String uploadDir = tempPath;
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String fileName = "shapefile_" + taskId + "_" + System.currentTimeMillis() + extension;
            String filePath = uploadDir + File.separator + fileName;

            // 保存文件
            File targetFile = new File(filePath);
            file.transferTo(targetFile);

            log.info("Shapefile文件保存成功 - 路径: {}", filePath);
            return filePath;

        } catch (Exception e) {
            log.error("保存上传的Shapefile文件失败 - 任务ID: {}", taskId, e);
            throw new IOException("文件保存失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ValidationResult validateShapefileData(String filePath, GisManageTemplate template,
                                                String target) {
        try {
            log.info("开始Shapefile数据验证 - 文件: {}, 模板ID: {}, 目标: {}", filePath, template.getId(), target);

            // 设置数据源
            if (template.getDataBase() != null) {
                DynamicDataSourceManager.build().useDataSource(template.getDataBase());
            }

            // 创建验证结果收集器
            ValidationResult result = new ValidationResult();
            result.setTotalRecords(0);
            result.setValidRecords(0);
            result.setErrorRecords(0);
            result.setErrors(new ArrayList<>());

            // 根据目标模式选择不同的处理方式
            if ("valid".equals(target)) {
                // 纯验证模式：只验证，不插入数据库
                log.info("执行纯验证模式 - 只验证数据，不执行数据库操作");
                result = performValidationOnly(filePath, template);
            } else if ("import".equals(target)) {
                // 直接导入模式：跳过验证，直接导入
                log.info("执行直接导入模式 - 跳过验证，直接导入数据库");
                result = performDirectImport(filePath, template);
            } else {
                result.setPassed(false);
                result.setSummary("不支持的目标模式: " + target);
                log.error("不支持的目标模式: {}", target);
            }

            log.info("Shapefile数据验证完成 - 总记录: {}, 有效: {}, 错误: {}, 通过: {}",
                    result.getTotalRecords(), result.getValidRecords(), result.getErrorRecords(), result.isPassed());

            return result;

        } catch (Exception e) {
            log.error("Shapefile数据验证失败 - 文件: {}", filePath, e);

            ValidationResult errorResult = new ValidationResult();
            errorResult.setTotalRecords(0);
            errorResult.setValidRecords(0);
            errorResult.setErrorRecords(1);
            errorResult.setPassed(false);
            errorResult.setErrors(new ArrayList<>());

            ValidationResult.ValidationError error = new ValidationResult.ValidationError();
            error.setRecordIndex(0);
            error.setFeatureId("SYSTEM_ERROR");
            error.setFieldName("SYSTEM");
            error.setErrorType(ValidationResult.ErrorType.BUSINESS_RULE_VIOLATION);
            error.setErrorMessage("验证过程异常: " + e.getMessage());
            error.setErrorLevel(ValidationResult.ErrorLevel.CRITICAL);
            errorResult.getErrors().add(error);

            return errorResult;
        }
    }

    /**
     * 执行纯验证模式（不插入数据库）
     */
    private ValidationResult performValidationOnly(String filePath, GisManageTemplate template) {
        log.info("执行纯验证模式 - 文件: {}, 模板: {}", filePath, template.getNameZh());

        try {
            // 调用DataValidationService进行详细验证（不插入数据库）
            // 传递null作为task参数，表示纯验证模式
            ValidationResult result = validateShapefileData(filePath, template,"valid");

            log.info("纯验证完成 - 总记录: {}, 有效: {}, 错误: {}, 通过: {}",
                    result.getTotalRecords(), result.getValidRecords(), result.getErrorRecords(), result.isPassed());

            return result;

        } catch (Exception e) {
            log.error("纯验证模式执行失败", e);
            ValidationResult errorResult = new ValidationResult();
            errorResult.setPassed(false);
            errorResult.setSummary("验证失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 执行直接导入模式（跳过验证）
     */
    private ValidationResult performDirectImport(String filePath, GisManageTemplate template) {
        log.info("执行直接导入模式 - 文件: {}, 模板: {}", filePath, template.getNameZh());

        try {
            // 直接执行导入，跳过验证步骤
            try (FileInputStream fis = new FileInputStream(new File(filePath))) {
                Map<String, Object> importResult = processShapefileWithTemplate(fis, new File(filePath).getName(), template.getId());

                // 构建导入结果
                ValidationResult result = new ValidationResult();
                boolean success = (Boolean) importResult.getOrDefault("success", false);
                Integer featuresProcessed = (Integer) importResult.getOrDefault("featuresProcessed", 0);

                result.setTotalRecords(featuresProcessed);
                result.setValidRecords(featuresProcessed);
                result.setErrorRecords(0);
                result.setPassed(success);

                if (success) {
                    result.setSummary("直接导入成功，导入了 " + featuresProcessed + " 条记录");
                    log.info("导入成功，导入了 {} 条记录", featuresProcessed);
                } else {
                    result.setSummary("直接导入失败: " + importResult.getOrDefault("message", "未知错误"));
                    log.warn("导入失败: {}", result.getSummary());
                }

                return result;
            }

        } catch (Exception e) {
            log.error("直接导入模式执行失败", e);
            ValidationResult errorResult = new ValidationResult();
            errorResult.setPassed(false);
            errorResult.setSummary("导入失败: " + e.getMessage());
            errorResult.setTotalRecords(0);
            errorResult.setValidRecords(0);
            errorResult.setErrorRecords(1);

            return errorResult;
        }
    }
}
