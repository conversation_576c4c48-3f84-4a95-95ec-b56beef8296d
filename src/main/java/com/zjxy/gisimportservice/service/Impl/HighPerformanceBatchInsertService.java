package com.zjxy.gisimportservice.service.Impl;

import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.mapper.TemplateDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class HighPerformanceBatchInsertService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TemplateDataMapper templateDataMapper;
    /**
     * 零转换超高速批量插入（基于动态表头映射）
     * @param entities 实体列表
     * @param template 模板配置
     * @param headerMapping Excel表头映射（列名->列索引），Excel模板必需此参数
     */
    public Map<String, Object> zeroConversionBatchInsert(List<GeoFeatureEntity> entities,
                                                        GisManageTemplate template,
                                                        Map<String, Integer> headerMapping) {
        DynamicDataSourceManager.build().useDataSource(template.getDataBase());
        log.info("零转换模式 - 表: {}, 数据库: {}, 数据已通过验证，直接执行插入", template.getTableName(), template.getDataBase());
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();

        try {
            if (entities == null || entities.isEmpty()) {
                result.put("success", false);
                result.put("message", "实体列表为空");
                return result;
            }

            // 1. 获取预构建的模板映射（基于动态表头映射）
            boolean isExcelTemplate = "excel".equalsIgnoreCase(template.getTemplateType());

            if (isExcelTemplate && headerMapping == null) {
                throw new IllegalArgumentException("Excel模板必须提供headerMapping参数");
            }

            log.info("构建模板映射 - 模板类型: {}, 表头映射大小: {}",
                    template.getTemplateType(), headerMapping != null ? headerMapping.size() : "N/A");
            TemplateDataMapper.TemplateMapping mapping = templateDataMapper.getTemplateMapping(template, headerMapping);

            log.info("零转换模式 - 表: {}, 字段数: {}, 数据已通过验证，直接执行插入",
                    mapping.getTableName(), mapping.getFieldOrder().size());

            int successCount = executeZeroConversionBatchInsert(entities, mapping);

            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;
            double speed = totalTime > 0 ? (successCount * 1000.0) / totalTime : 0;

            log.info("零转换批量插入完成 - , 耗时: {} ms, 速度: {:.0f} 条/秒",totalTime, speed);

            result.put("success", true);
            result.put("insertedCount", successCount);
            result.put("totalTime", totalTime);
            result.put("speed", speed);
            result.put("conversionMode", "零转换模式");

        } catch (Exception e) {
            log.error("零转换批量插入失败 - 模板: {}, 表名: {}, 数据量: {}",
                     template.getNameZh(), template.getTableName(), entities.size(), e);
            result.put("success", false);
            result.put("message", "插入失败: " + e.getMessage());
            result.put("errorDetails", e.getClass().getSimpleName());
        }

        return result;
    }


    /**
     * 验证实体数据样本 - 在插入失败时检验实体具体数据
     */
    private void validateEntityDataSample(List<GeoFeatureEntity> entities, TemplateDataMapper.TemplateMapping mapping, int sampleSize) {
        log.error("=== 实体数据验证 (前{}条) ===", sampleSize);

        Map<String, String> fieldTypeMapping = mapping.getFieldTypeMapping();

        for (int i = 0; i < sampleSize && i < entities.size(); i++) {
            GeoFeatureEntity entity = entities.get(i);
            log.error("实体[{}] 数据验证:", i);

            for (String dbField : mapping.getFieldOrder()) {
                if (dbField.equals(mapping.getGeometryField())) {
                    continue; // 几何字段跳过检查
                }

                String sourceField = mapping.getSourceFieldName(dbField);
                Object value = entity.getAttribute(sourceField);
                String dbFieldType = fieldTypeMapping.get(dbField);

                if (value != null) {
                    boolean compatible = isValueTypeCompatible(value, dbFieldType);
                    log.error("  字段 '{}' -> '{}': 值='{}' (Java类型: {}) -> 目标类型: {} [{}]",
                             sourceField, dbField, value,
                             value.getClass().getSimpleName(), dbFieldType,
                             compatible ? "兼容" : "不兼容");
                } else {
                    log.error("  字段 '{}' -> '{}': 值=null -> 目标类型: {}",
                             sourceField, dbField, dbFieldType);
                }
            }
        }
        log.error("=== 实体数据验证结束 ===");
    }



    /**
     * 检查值类型是否兼容
     */
    private boolean isValueTypeCompatible(Object value, String dbFieldType) {
        if (dbFieldType == null) return true;

        String fieldType = dbFieldType.toLowerCase();
        Class<?> valueClass = value.getClass();

        // 数值类型检查
        if (fieldType.contains("double") || fieldType.contains("float") || fieldType.contains("real")) {
            return valueClass == Double.class || valueClass == Float.class ||
                   (valueClass == String.class && isNumericString(value.toString()));
        }

        if (fieldType.contains("integer") || fieldType.contains("int") || fieldType.contains("bigint")) {
            return valueClass == Integer.class || valueClass == Long.class ||
                   (valueClass == String.class && isIntegerString(value.toString()));
        }

        if (fieldType.contains("boolean") || fieldType.contains("bool")) {
            return valueClass == Boolean.class ||
                   (valueClass == String.class && isBooleanString(value.toString()));
        }

        // 字符串类型总是兼容的
        return true;
    }

    /**
     * 执行零转换批量插入 - 直接使用原始值，跳过类型转换
     */
    private int executeZeroConversionBatchInsert(List<GeoFeatureEntity> entities, TemplateDataMapper.TemplateMapping mapping) {
        final int OPTIMAL_BATCH_SIZE = calculateOptimalBatchSize(entities.size());
        int totalSuccess = 0;

        log.info("零转换批量插入 - 总数: {} 条, 批次大小: {} 条", entities.size(), OPTIMAL_BATCH_SIZE);

        // 分批处理
        int totalBatches = (int) Math.ceil((double) entities.size() / OPTIMAL_BATCH_SIZE);

        for (int i = 0; i < entities.size(); i += OPTIMAL_BATCH_SIZE) {
            int endIndex = Math.min(i + OPTIMAL_BATCH_SIZE, entities.size());
            List<GeoFeatureEntity> batch = entities.subList(i, endIndex);
            int currentBatch = (i / OPTIMAL_BATCH_SIZE) + 1;

            long batchStartTime = System.currentTimeMillis();

            try {
                // 直接构建参数数组，跳过类型转换
                List<Object[]> batchParams = buildZeroConversionParams(batch, mapping);

                // 检查并过滤重复的主键
                List<Object[]> filteredParams = filterDuplicateKeys(batchParams, mapping);

                if (filteredParams.isEmpty()) {
                    log.warn("批次 {}/{} 中所有数据都存在主键冲突，跳过插入", currentBatch, totalBatches);
                    continue;
                }

                // 执行批量插入
                int[] updateCounts;
                try {
                    updateCounts = jdbcTemplate.batchUpdate(mapping.getInsertSQL(), filteredParams);
                } catch (Exception e) {
                    log.error("批次插入SQL执行失败: {}", e.getMessage());
                    // 如果是事务被中止的错误，需要重新开始事务
                    if (e.getMessage().contains("current transaction is aborted")) {
                        log.error("检测到事务中止错误，需要重新开始事务");
                        throw new RuntimeException("数据库事务被中止，请检查数据格式和约束条件", e);
                    }
                    throw e;
                }

                int batchSuccess = Arrays.stream(updateCounts).sum();
                totalSuccess += batchSuccess;

                long batchTime = System.currentTimeMillis() - batchStartTime;

                // 只在每10个批次或最后一个批次时输出日志
                if (currentBatch % 10 == 0 || currentBatch == totalBatches) {
                    double batchSpeed = batchTime > 0 ? (batchSuccess * 1000.0) / batchTime : 0;
                    log.info("零转换进度 - 批次: {}, 累计成功: {} 条, 当前速度: {}条/秒",
                            currentBatch, totalSuccess, batchSpeed);
                }

            } catch (Exception e) {
                log.error("零转换批次插入失败 - 批次: {}/{}, 大小: {}, 错误: {}",
                         currentBatch, totalBatches, batch.size(), e.getMessage());

                // 检验实体具体数据，打印前5条数据的详细信息
                validateEntityDataSample(batch, mapping, Math.min(5, batch.size()));

                // 打印SQL语句用于调试
                log.error("失败的SQL语句: {}", mapping.getInsertSQL());

                // 记录失败信息，稍后在新服务中调试
                log.error("批次插入失败，需要单独调试前几条数据");

                // 抛出异常
                throw new RuntimeException(String.format("零转换批量插入失败 - 批次: %d/%d, 错误: %s",
                                                        currentBatch, totalBatches, e.getMessage()), e);
            }
        }

        return totalSuccess;
    }

    /**
     * 构建零转换参数数组 - 直接使用原始值，跳过类型转换
     */
    private List<Object[]> buildZeroConversionParams(List<GeoFeatureEntity> entities, TemplateDataMapper.TemplateMapping mapping) {
        List<String> fieldOrder = mapping.getFieldOrder();
        String geometryField = mapping.getGeometryField();
        List<Object[]> batchParams = new ArrayList<>(entities.size());

        log.debug("开始构建批量插入参数，实体数量: {}, 字段顺序: {}", entities.size(), fieldOrder);
        log.debug("几何字段: {}", geometryField);

        for (int entityIndex = 0; entityIndex < entities.size(); entityIndex++) {
            GeoFeatureEntity entity = entities.get(entityIndex);
            Object[] params = new Object[fieldOrder.size()];

            if (entityIndex < 3) { // 只记录前3条数据的详细日志
                log.debug("处理实体 {}: featureId={}", entityIndex, entity.getFeatureId());
                log.debug("实体属性: {}", entity.getRawAttributes());
            }

            for (int i = 0; i < fieldOrder.size(); i++) {
                String dbField = fieldOrder.get(i);

                if (dbField.equals(geometryField)) {
                    // 几何字段
                    params[i] = entity.getGeometry();
                    if (entityIndex < 3) {
                        log.debug("  几何字段 {}: {}", dbField, params[i]);
                    }
                } else {
                    // 普通字段 - 需要进行类型转换以匹配PostgreSQL要求
                    String sourceField = mapping.getSourceFieldName(dbField);
                    Object value = entity.getAttribute(sourceField);

                    // 如果通过sourceField找不到值，尝试直接使用dbField
                    if (value == null && !sourceField.equals(dbField)) {
                        value = entity.getAttribute(dbField);
                        log.debug("  通过dbField找到值: {} = {}", dbField, value);
                    }

                    // 根据数据库字段类型进行类型转换
                    String dbFieldType = mapping.getFieldTypeMapping().get(dbField);
                    Object convertedValue = convertValueForDatabase(value, dbFieldType, dbField);

                    params[i] = convertedValue;
                    if (entityIndex < 3) {
                        log.debug("  数据字段 {} (源字段: {}): {} -> {} (类型: {})",
                                 dbField, sourceField, value, convertedValue, dbFieldType);
                    }
                }
            }

            batchParams.add(params);
            if (entityIndex < 3) {
                log.debug("实体 {} 参数构建完成: {}", entityIndex, java.util.Arrays.toString(params));
            }
        }

        // 移除详细日志输出以提升性能
        return batchParams;
    }

    /**
     * 为数据库转换值类型
     */
    private Object convertValueForDatabase(Object value, String dbFieldType, String fieldName) {
        if (value == null) {
            return null;
        }

        if (dbFieldType == null) {
            return value;
        }

        String fieldType = dbFieldType.toLowerCase();

        try {
            // 处理double precision类型
            if (fieldType.contains("double") || fieldType.contains("float") || fieldType.contains("real")) {
                if (value instanceof String) {
                    String strValue = ((String) value).trim();
                    if (strValue.isEmpty()) {
                        return null;
                    }
                    return Double.parseDouble(strValue);
                } else if (value instanceof Number) {
                    return ((Number) value).doubleValue();
                }
                return value;
            }

            // 处理integer类型
            if (fieldType.contains("integer") || fieldType.contains("int") || fieldType.contains("bigint")) {
                if (value instanceof String) {
                    String strValue = ((String) value).trim();
                    if (strValue.isEmpty()) {
                        return null;
                    }
                    return Long.parseLong(strValue);
                } else if (value instanceof Number) {
                    return ((Number) value).longValue();
                }
                return value;
            }

            // 处理boolean类型
            if (fieldType.contains("boolean") || fieldType.contains("bool")) {
                if (value instanceof String) {
                    String strValue = ((String) value).trim().toLowerCase();
                    return "true".equals(strValue) || "1".equals(strValue) || "yes".equals(strValue);
                } else if (value instanceof Boolean) {
                    return value;
                }
                return Boolean.parseBoolean(value.toString());
            }

            // 字符串类型直接返回
            return value;

        } catch (NumberFormatException e) {
            log.warn("字段 {} 的值 '{}' 无法转换为类型 {}, 使用null", fieldName, value, dbFieldType);
            return null;
        }
    }

    /**
     * 辅助方法：检查字符串是否为数值
     */
    private boolean isNumericString(String str) {
        try {
            Double.parseDouble(str.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 辅助方法：检查字符串是否为整数
     */
    private boolean isIntegerString(String str) {
        try {
            Long.parseLong(str.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 辅助方法：检查字符串是否为布尔值
     */
    private boolean isBooleanString(String str) {
        String trimmed = str.trim().toLowerCase();
        return "true".equals(trimmed) || "false".equals(trimmed) || "1".equals(trimmed) || "0".equals(trimmed);
    }


    /**
     * 计算最优批次大小 - 针对大数据量优化
     */
    private int calculateOptimalBatchSize(int totalSize) {
        if (totalSize <= 1000) {
            return Math.min(totalSize, 500);  // 小数据量使用较大批次
        } else if (totalSize <= 10000) {
            return 1000;  // 中等数据量
        } else if (totalSize <= 50000) {
            return 1000;   // 大数据量使用较小批次
        } else {
            return 2000;   // 超大数据量使用最小批次
        }
    }



    /**
     * 检查是否为必需字段
     */
    private boolean isRequiredField(String fieldName) {
        // 常见的必需字段列表
        return "unique_code".equalsIgnoreCase(fieldName) ||
               "id".equalsIgnoreCase(fieldName) ||
               "uuid".equalsIgnoreCase(fieldName);
    }

    /**
     * 为必需字段生成默认值
     */
    private Object generateDefaultValue(String fieldName) {
        if ("unique_code".equalsIgnoreCase(fieldName) ||
            "uuid".equalsIgnoreCase(fieldName)) {
            // 生成UUID
            return UUID.randomUUID().toString();
        } else if ("id".equalsIgnoreCase(fieldName)) {
            // 生成随机ID
            return System.currentTimeMillis();
        }
        return null;
    }

    /**
     * 过滤重复的主键
     */
    private List<Object[]> filterDuplicateKeys(List<Object[]> batchParams, TemplateDataMapper.TemplateMapping mapping) {
        // 简单实现：暂时返回原始参数，后续可以添加主键检查逻辑
        return batchParams;
    }

}
