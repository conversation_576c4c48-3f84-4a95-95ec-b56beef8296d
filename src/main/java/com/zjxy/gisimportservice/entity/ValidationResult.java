package com.zjxy.gisimportservice.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * 数据验证结果实体
 * 用于封装数据检查的详细结果信息
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-18
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ValidationResult {

    /**
     * 验证是否通过
     */
    private boolean passed;

    /**
     * 总记录数
     */
    private int totalRecords;

    /**
     * 通过验证的记录数
     */
    private int validRecords;

    /**
     * 错误记录数（字段级错误总数）
     */
    private int errorRecords;

    /**
     * 有错误的记录数（包含至少一个错误的记录数量）
     */
    private int recordsWithErrors;

    /**
     * 错误率（百分比）- 字段级错误率
     */
    private double errorRate;

    /**
     * 记录错误率（百分比）- 有错误记录数 / 总记录数
     */
    private double recordErrorRate;

    /**
     * 字段错误率（百分比）- 总错误数 / (总记录数 × 字段数量)
     */
    private double fieldErrorRate;

    /**
     * 验证的字段数量
     */
    private int totalFields;

    /**
     * 验证开始时间
     */
    private Timestamp startTime;

    /**
     * 验证结束时间
     */
    private Timestamp endTime;

    /**
     * 验证耗时（毫秒）
     */
    private long durationMs;

    /**
     * 错误文件路径
     */
    private String errorFilePath;

    /**
     * SHP文件路径（用于后续处理，避免重复查找）
     */
    private String shpFilePath;

    /**
     * 错误统计信息
     */
    private Map<String, Integer> errorStatistics;

    /**
     * 错误详情列表
     */
    private List<ValidationError> errors;

    /**
     * 验证摘要信息
     */
    private String summary;

    /**
     * 验证配置信息
     */
    private ValidationConfig config;

    /**
     * 数据验证错误实体
     */
    @Data
    public static class ValidationError {
        /**
         * 记录索引（从0开始）
         */
        private int recordIndex;

        /**
         * 要素ID
         */
        private String featureId;

        /**
         * 错误字段名
         */
        private String fieldName;

        /**
         * 错误类型
         */
        private ErrorType errorType;

        /**
         * 错误描述
         */
        private String errorMessage;

        /**
         * 原始值
         */
        private Object originalValue;

        /**
         * 期望类型
         */
        private String expectedType;

        /**
         * 建议修复方案
         */
        private String suggestion;

        /**
         * 错误严重级别
         */
        private ErrorLevel errorLevel;
    }

    /**
     * 验证配置
     */
    @Data
    public static class ValidationConfig {
        /**
         * 最大错误率（百分比）
         */
        private double maxErrorRate = 10.0;

        /**
         * 是否启用严格模式
         */
        private boolean strictMode = false;

        /**
         * 是否检查必填字段
         */
        private boolean checkRequiredFields = false;

        /**
         * 是否检查数据类型
         */
        private boolean checkDataTypes = true;


        /**
         * 是否检查数据格式
         */
        private boolean checkDataFormat = true;

        /**
         * 是否检查几何数据
         */
        private boolean checkGeometry = true;

        /**
         * 批次大小
         */
        private int batchSize = 1000;
    }

    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        FIELD_MISSING("字段缺失"),
        TYPE_MISMATCH("类型不匹配"),
        FORMAT_ERROR("格式错误"),
        REQUIRED_FIELD_EMPTY("必填项为空"),
        VALUE_OUT_OF_RANGE("值超出范围"),
        GEOMETRY_INVALID("几何数据无效"),
        DUPLICATE_VALUE("重复值"),
        REFERENCE_ERROR("引用错误"),
        CONVERSION_FAILED("类型转换失败"),
        TEMPLATE_MAPPING_ERROR("模板映射错误"),

        // 字段级验证错误类型
        COORDINATE_INVALID("坐标无效"),
        BUSINESS_RULE_VIOLATION("业务规则违反"),
        FIELD_VALIDATION_FAILED("字段验证失败"),
        VALIDATION_FAILED("验证失败");

        private final String description;

        ErrorType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 错误严重级别枚举
     */
    public enum ErrorLevel {
        WARNING("警告"),
        ERROR("错误"),
        CRITICAL("严重错误");

        private final String description;

        ErrorLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 计算错误率
     */
    public void calculateErrorRate() {
        if (totalRecords > 0) {
            this.errorRate = (errorRecords * 100.0) / totalRecords;
        } else {
            this.errorRate = 0.0;
        }
    }

    /**
     * 计算验证耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.durationMs = endTime.getTime() - startTime.getTime();
        }
    }

    /**
     * 判断是否通过验证
     * 基于记录错误率进行判断，更符合业务逻辑
     */
    public boolean isPassed() {
        double maxErrorRate = config != null ? config.getMaxErrorRate() : 10.0;
        // 使用记录错误率进行判断，这样错误率永远不会超过100%
        return recordErrorRate <= maxErrorRate;
    }

    /**
     * 判断是否通过验证（基于字段错误率）
     * 保留原有逻辑用于兼容性
     */
    public boolean isPassedByFieldErrorRate() {
        return errorRate <= (config != null ? config.getMaxErrorRate() : 10.0);
    }

    /**
     * 生成验证摘要
     */
    public void generateSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("数据验证完成：");
        sb.append("总记录数 ").append(totalRecords).append(" 条，");
        sb.append("有效记录 ").append(validRecords).append(" 条，");
        sb.append("有错误记录 ").append(recordsWithErrors).append(" 条，");
        sb.append("字段错误总数 ").append(errorRecords).append(" 个；");

        sb.append("记录错误率 ").append(String.format("%.2f", recordErrorRate)).append("%，");
        sb.append("字段错误率 ").append(String.format("%.2f", fieldErrorRate)).append("%");

        if (passed) {
            sb.append("，验证通过");
        } else {
            sb.append("，验证失败");
        }

        if (durationMs > 0) {
            sb.append("，耗时 ").append(durationMs).append(" 毫秒");
        }

        this.summary = sb.toString();
    }
}
