package com.zjxy.gisimportservice;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

@SpringBootApplication(scanBasePackages = "com.zjxy")
@ComponentScan(basePackages = "com.zjxy",
               excludeFilters = {
                   @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
                                       classes = com.zjxy.framework.config.SwaggerConfig.class),
                   @ComponentScan.Filter(type = FilterType.REGEX,
                                       pattern = "springfox.*")
               })
@MapperScan(basePackages = {
    "com.zjxy.framework.mapper",
    "com.zjxy.gisimportservice.mapper"
})
public class GisImportServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(GisImportServiceApplication.class, args);
    }

}
