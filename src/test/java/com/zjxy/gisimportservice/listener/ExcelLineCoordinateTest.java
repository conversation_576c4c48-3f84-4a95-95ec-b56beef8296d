package com.zjxy.gisimportservice.listener;

import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.service.DataValidationService;
import com.zjxy.gisimportservice.service.Impl.CoordinateTransformService;
import com.zjxy.gisimportservice.service.Impl.HighPerformanceBatchInsertService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Excel线表坐标处理测试
 */
public class ExcelLineCoordinateTest {

    @Mock
    private DataValidationService validationService;

    @Mock
    private CoordinateTransformService coordinateService;

    @Mock
    private HighPerformanceBatchInsertService batchInsertService;

    private GisManageTemplate template;
    private ExcelDataListener listener;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 创建Excel线表模板
        template = new GisManageTemplate();
        template.setTemplateType("excel");
        template.setType(3); // 线表
        template.setIsZh(true); // 需要坐标转换
        template.setOriginalCoordinateSystem("WenZhou2000");
        template.setTargetCoordinateSystem("CGCS2000");
        
        // 设置线坐标映射配置
        Map<String, Object> lineMap = new HashMap<>();
        lineMap.put("x", 5);   // 起点X在第5列
        lineMap.put("y", 6);   // 起点Y在第6列
        lineMap.put("x1", 9);  // 终点X在第9列
        lineMap.put("y1", 10); // 终点Y在第10列
        lineMap.put("qdbh", "");
        lineMap.put("zdbh", "");
        lineMap.put("targetPointTable", "");
        lineMap.put("pointTableBh", "");
        template.setLineMap(lineMap);
        
        // 模拟坐标转换服务
        when(coordinateService.transformGeometry(anyString(), anyString(), anyString()))
            .thenAnswer(invocation -> {
                String wkt = invocation.getArgument(0);
                // 简单的模拟转换：将坐标值加1000
                if (wkt.startsWith("LINESTRING")) {
                    return wkt.replace("459048.432", "460048.432")
                             .replace("3056094.039", "3057094.039")
                             .replace("459039.772", "460039.772")
                             .replace("3056064.731", "3057064.731");
                }
                return wkt;
            });
        
        // 创建监听器
        listener = new ExcelDataListener(template, validationService, coordinateService, 
                                       batchInsertService, 1000, "import");
    }

    @Test
    void testLineCoordinateProcessing() {
        // 准备Excel行数据
        Map<Integer, Object> rowData = new HashMap<>();
        rowData.put(0, "管道001");           // 管道编号
        rowData.put(1, "PE");               // 材质
        rowData.put(2, "200");              // 管径
        rowData.put(5, "459048.432");       // 起点X
        rowData.put(6, "3056094.039");      // 起点Y
        rowData.put(9, "459039.772");       // 终点X
        rowData.put(10, "3056064.731");     // 终点Y
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = ExcelDataListener.class
                .getDeclaredMethod("processCoordinateFields", Map.class, Map.class);
            method.setAccessible(true);
            
            Map<String, Object> attributes = new HashMap<>();
            method.invoke(listener, rowData, attributes);
            
            // 验证坐标解析结果
            assertEquals(459048.432, (Double) attributes.get("x_coord"), 0.001);
            assertEquals(3056094.039, (Double) attributes.get("y_coord"), 0.001);
            assertEquals(459039.772, (Double) attributes.get("x1_coord"), 0.001);
            assertEquals(3056064.731, (Double) attributes.get("y1_coord"), 0.001);
            
            // 验证WKT几何生成
            String geometry = (String) attributes.get("geometry");
            assertNotNull(geometry);
            assertTrue(geometry.startsWith("LINESTRING"));
            assertTrue(geometry.contains("459048.432 3056094.039"));
            assertTrue(geometry.contains("459039.772 3056064.731"));
            
        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    void testLineCoordinateTransformation() {
        // 创建包含坐标的实体
        GeoFeatureEntity entity = new GeoFeatureEntity();
        entity.setFeatureId("test_line_1");
        entity.setAttribute("x_coord", 459048.432);
        entity.setAttribute("y_coord", 3056094.039);
        entity.setAttribute("x1_coord", 459039.772);
        entity.setAttribute("y1_coord", 3056064.731);
        entity.setAttribute("col_5", 459048.432);  // 原始起点X字段
        entity.setAttribute("col_6", 3056094.039); // 原始起点Y字段
        entity.setAttribute("col_9", 459039.772);  // 原始终点X字段
        entity.setAttribute("col_10", 3056064.731); // 原始终点Y字段
        entity.setGeometry("LINESTRING(459048.432 3056094.039, 459039.772 3056064.731)");

        // 使用反射调用坐标转换方法
        try {
            java.lang.reflect.Method method = ExcelDataListener.class
                .getDeclaredMethod("transformExcelLineCoordinates",
                                 GeoFeatureEntity.class, String.class, String.class);
            method.setAccessible(true);

            method.invoke(listener, entity, "WenZhou2000", "CGCS2000");

            // 验证转换后的坐标（模拟转换是加1000）
            assertEquals(460048.432, (Double) entity.getAttribute("x_coord"), 0.001);
            assertEquals(3057094.039, (Double) entity.getAttribute("y_coord"), 0.001);
            assertEquals(460039.772, (Double) entity.getAttribute("x1_coord"), 0.001);
            assertEquals(3057064.731, (Double) entity.getAttribute("y1_coord"), 0.001);

            // 验证原始字段也被更新（这是关键修复）
            assertEquals(460048.432, (Double) entity.getAttribute("col_5"), 0.001);
            assertEquals(3057094.039, (Double) entity.getAttribute("col_6"), 0.001);
            assertEquals(460039.772, (Double) entity.getAttribute("col_9"), 0.001);
            assertEquals(3057064.731, (Double) entity.getAttribute("col_10"), 0.001);

            // 验证几何也被更新
            String transformedGeometry = entity.getGeometry();
            assertTrue(transformedGeometry.contains("460048.432"));
            assertTrue(transformedGeometry.contains("3057094.039"));

        } catch (Exception e) {
            fail("坐标转换测试失败: " + e.getMessage());
        }
    }

    @Test
    void testWktParsing() {
        // 使用反射测试WKT解析方法
        try {
            java.lang.reflect.Method method = ExcelDataListener.class
                .getDeclaredMethod("parseLineFromWkt", String.class);
            method.setAccessible(true);
            
            String wkt = "LINESTRING(459048.432 3056094.039, 459039.772 3056064.731)";
            double[][] coords = (double[][]) method.invoke(listener, wkt);
            
            assertNotNull(coords);
            assertEquals(2, coords.length);
            
            // 验证起点坐标
            assertEquals(459048.432, coords[0][0], 0.001);
            assertEquals(3056094.039, coords[0][1], 0.001);
            
            // 验证终点坐标
            assertEquals(459039.772, coords[1][0], 0.001);
            assertEquals(3056064.731, coords[1][1], 0.001);
            
        } catch (Exception e) {
            fail("WKT解析测试失败: " + e.getMessage());
        }
    }

    @Test
    void testTemplateTypeValidation() {
        // 测试非Excel模板跳过处理
        template.setTemplateType("shp");
        
        Map<Integer, Object> rowData = new HashMap<>();
        rowData.put(5, "459048.432");
        rowData.put(6, "3056094.039");
        
        try {
            java.lang.reflect.Method method = ExcelDataListener.class
                .getDeclaredMethod("processCoordinateFields", Map.class, Map.class);
            method.setAccessible(true);
            
            Map<String, Object> attributes = new HashMap<>();
            method.invoke(listener, rowData, attributes);
            
            // 非Excel模板应该跳过坐标处理
            assertNull(attributes.get("x_coord"));
            assertNull(attributes.get("geometry"));
            
        } catch (Exception e) {
            fail("模板类型验证测试失败: " + e.getMessage());
        }
    }
}
