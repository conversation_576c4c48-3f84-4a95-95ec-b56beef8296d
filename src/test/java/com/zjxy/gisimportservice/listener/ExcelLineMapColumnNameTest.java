package com.zjxy.gisimportservice.listener;

import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.service.DataValidationService;
import com.zjxy.gisimportservice.service.Impl.CoordinateTransformService;
import com.zjxy.gisimportservice.service.Impl.HighPerformanceBatchInsertService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Excel线表基于列名映射功能测试
 */
public class ExcelLineMapColumnNameTest {

    @Mock
    private DataValidationService validationService;

    @Mock
    private CoordinateTransformService coordinateService;

    @Mock
    private HighPerformanceBatchInsertService batchInsertService;

    private GisManageTemplate template;
    private ExcelDataListener listener;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // 创建Excel线表模板
        template = new GisManageTemplate();
        template.setTemplateType("excel");
        template.setType(3); // 线表
        template.setExcelDataStartRow(2); // 数据从第2行开始，第1行是表头
        template.setIsZh(false); // 不进行坐标转换，简化测试

        // 设置新格式的线坐标映射配置：直接使用列名作为值
        Map<String, Object> lineMap = new HashMap<>();
        lineMap.put("x", "起点横坐标");      // 起点X坐标列名
        lineMap.put("y", "起点纵坐标");      // 起点Y坐标列名
        lineMap.put("x1", "终点横坐标");     // 终点X坐标列名
        lineMap.put("y1", "终点纵坐标");     // 终点Y坐标列名
        lineMap.put("qdbh", "");           // 起点编号（空值）
        lineMap.put("zdbh", "");           // 终点编号（空值）
        lineMap.put("targetPointTable", "");
        lineMap.put("pointTableBh", "");
        template.setLineMap(lineMap);

        // 创建监听器
        listener = new ExcelDataListener(template, validationService, coordinateService,
                                       batchInsertService, 1000, "import");
    }

    @Test
    void testLineMapColumnNameMapping() {
        // 准备表头数据
        Map<Integer, Object> headerData = new HashMap<>();
        headerData.put(0, "管道编号");
        headerData.put(1, "材质");
        headerData.put(2, "起点横坐标");
        headerData.put(3, "起点纵坐标");
        headerData.put(4, "终点横坐标");
        headerData.put(5, "终点纵坐标");

        // 准备行数据
        Map<Integer, Object> rowData = new HashMap<>();
        rowData.put(0, "PIPE001");
        rowData.put(1, "PE");
        rowData.put(2, "492670.037");    // 起点X
        rowData.put(3, "3107642.153");   // 起点Y
        rowData.put(4, "492670.411");    // 终点X
        rowData.put(5, "3107640.474");   // 终点Y

        try {
            // 先读取表头
            java.lang.reflect.Method readHeaderMethod = ExcelDataListener.class
                .getDeclaredMethod("readHeader", Map.class);
            readHeaderMethod.setAccessible(true);
            readHeaderMethod.invoke(listener, headerData);

            // 处理坐标字段
            java.lang.reflect.Method processCoordinatesMethod = ExcelDataListener.class
                .getDeclaredMethod("processCoordinateFields", Map.class, Map.class);
            processCoordinatesMethod.setAccessible(true);

            Map<String, Object> attributes = new HashMap<>();
            processCoordinatesMethod.invoke(listener, rowData, attributes);

            // 验证坐标解析结果
            assertEquals(492670.037, (Double) attributes.get("x_coord"), 0.001);
            assertEquals(3107642.153, (Double) attributes.get("y_coord"), 0.001);
            assertEquals(492670.411, (Double) attributes.get("x1_coord"), 0.001);
            assertEquals(3107640.474, (Double) attributes.get("y1_coord"), 0.001);

            // 验证WKT几何生成
            String geometry = (String) attributes.get("geometry");
            assertNotNull(geometry);
            assertTrue(geometry.startsWith("LINESTRING"));
            assertTrue(geometry.contains("492670.037 3107642.153"));
            assertTrue(geometry.contains("492670.411 3107640.474"));

        } catch (Exception e) {
            fail("线表列名映射测试失败: " + e.getMessage());
        }
    }

    @Test
    void testMissingLineCoordinateColumns() {
        // 设置一个缺少坐标列的表头
        Map<Integer, Object> headerData = new HashMap<>();
        headerData.put(0, "管道编号");
        headerData.put(1, "材质");
        headerData.put(2, "起点横坐标");
        headerData.put(3, "起点纵坐标");
        // 缺少"终点横坐标"和"终点纵坐标"列

        try {
            java.lang.reflect.Method method = ExcelDataListener.class
                .getDeclaredMethod("readHeader", Map.class);
            method.setAccessible(true);

            // 应该抛出异常，因为缺少终点坐标列
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                try {
                    method.invoke(listener, headerData);
                } catch (Exception e) {
                    if (e.getCause() instanceof RuntimeException) {
                        throw (RuntimeException) e.getCause();
                    }
                    throw new RuntimeException(e);
                }
            });

            assertTrue(exception.getMessage().contains("Excel表中未找到以下列名"));
            assertTrue(exception.getMessage().contains("终点横坐标"));
            assertTrue(exception.getMessage().contains("终点纵坐标"));

        } catch (Exception e) {
            fail("缺失坐标列验证测试失败: " + e.getMessage());
        }
    }

    @Test
    void testGetCoordinateColumnIndex() {
        // 准备表头数据
        Map<Integer, Object> headerData = new HashMap<>();
        headerData.put(0, "起点横坐标");
        headerData.put(1, "起点纵坐标");
        headerData.put(2, "终点横坐标");
        headerData.put(3, "终点纵坐标");

        try {
            // 先读取表头
            java.lang.reflect.Method readHeaderMethod = ExcelDataListener.class
                .getDeclaredMethod("readHeader", Map.class);
            readHeaderMethod.setAccessible(true);
            readHeaderMethod.invoke(listener, headerData);

            // 测试getCoordinateColumnIndex方法
            java.lang.reflect.Method getIndexMethod = ExcelDataListener.class
                .getDeclaredMethod("getCoordinateColumnIndex", Map.class, String.class);
            getIndexMethod.setAccessible(true);

            Map<String, Object> lineMap = template.getLineMap();

            // 验证各个坐标字段的列索引
            Integer xIndex = (Integer) getIndexMethod.invoke(listener, lineMap, "x");
            Integer yIndex = (Integer) getIndexMethod.invoke(listener, lineMap, "y");
            Integer x1Index = (Integer) getIndexMethod.invoke(listener, lineMap, "x1");
            Integer y1Index = (Integer) getIndexMethod.invoke(listener, lineMap, "y1");

            assertEquals(Integer.valueOf(0), xIndex);   // "起点横坐标" -> 列索引0
            assertEquals(Integer.valueOf(1), yIndex);   // "起点纵坐标" -> 列索引1
            assertEquals(Integer.valueOf(2), x1Index);  // "终点横坐标" -> 列索引2
            assertEquals(Integer.valueOf(3), y1Index);  // "终点纵坐标" -> 列索引3

        } catch (Exception e) {
            fail("坐标列索引获取测试失败: " + e.getMessage());
        }
    }

    @Test
    void testInvalidIntegerConfigurationThrowsException() {
        // 测试使用整数配置会抛出异常（不再支持向后兼容）
        Map<String, Object> lineMap = new HashMap<>();
        lineMap.put("x", 0);    // 整数位置（不再支持）
        lineMap.put("y", "起点纵坐标");    // 正确的字符串配置
        lineMap.put("x1", "终点横坐标");   // 正确的字符串配置
        lineMap.put("y1", "终点纵坐标");   // 正确的字符串配置
        template.setLineMap(lineMap);

        try {
            java.lang.reflect.Method getIndexMethod = ExcelDataListener.class
                .getDeclaredMethod("getCoordinateColumnIndex", Map.class, String.class);
            getIndexMethod.setAccessible(true);

            // 应该抛出异常，因为不再支持整数配置
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                try {
                    getIndexMethod.invoke(listener, lineMap, "x");
                } catch (Exception e) {
                    if (e.getCause() instanceof RuntimeException) {
                        throw (RuntimeException) e.getCause();
                    }
                    throw new RuntimeException(e);
                }
            });

            assertTrue(exception.getMessage().contains("配置值必须是字符串类型的列名"));
            assertTrue(exception.getMessage().contains("正确的配置格式示例"));

        } catch (Exception e) {
            fail("整数配置异常测试失败: " + e.getMessage());
        }
    }

    @Test
    void testEmptyCoordinateConfigurationThrowsException() {
        // 测试空的坐标配置会抛出异常（强制验证）
        Map<String, Object> lineMap = new HashMap<>();
        lineMap.put("x", "");      // 空字符串（不允许）
        lineMap.put("y", "起点纵坐标");    // 正确的字符串配置
        lineMap.put("x1", "终点横坐标");   // 正确的字符串配置
        lineMap.put("y1", "终点纵坐标");   // 正确的字符串配置
        template.setLineMap(lineMap);

        try {
            java.lang.reflect.Method getIndexMethod = ExcelDataListener.class
                .getDeclaredMethod("getCoordinateColumnIndex", Map.class, String.class);
            getIndexMethod.setAccessible(true);

            // 空字符串应该抛出异常
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                try {
                    getIndexMethod.invoke(listener, lineMap, "x");
                } catch (Exception e) {
                    if (e.getCause() instanceof RuntimeException) {
                        throw (RuntimeException) e.getCause();
                    }
                    throw new RuntimeException(e);
                }
            });

            assertTrue(exception.getMessage().contains("列名不能为空字符串"));
            assertTrue(exception.getMessage().contains("请配置有效的Excel列名"));

        } catch (Exception e) {
            fail("空字符串配置异常测试失败: " + e.getMessage());
        }
    }

    @Test
    void testNullCoordinateConfigurationThrowsException() {
        // 测试null坐标配置会抛出异常
        Map<String, Object> lineMap = new HashMap<>();
        lineMap.put("x", "起点横坐标");    // 正确的字符串配置
        lineMap.put("y", null);           // null值（不允许）
        lineMap.put("x1", "终点横坐标");   // 正确的字符串配置
        lineMap.put("y1", "终点纵坐标");   // 正确的字符串配置
        template.setLineMap(lineMap);

        try {
            java.lang.reflect.Method getIndexMethod = ExcelDataListener.class
                .getDeclaredMethod("getCoordinateColumnIndex", Map.class, String.class);
            getIndexMethod.setAccessible(true);

            // null值应该抛出异常
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                try {
                    getIndexMethod.invoke(listener, lineMap, "y");
                } catch (Exception e) {
                    if (e.getCause() instanceof RuntimeException) {
                        throw (RuntimeException) e.getCause();
                    }
                    throw new RuntimeException(e);
                }
            });

            assertTrue(exception.getMessage().contains("配置值不能为空"));
            assertTrue(exception.getMessage().contains("请使用列名字符串配置"));

        } catch (Exception e) {
            fail("null配置异常测试失败: " + e.getMessage());
        }
    }
        lineMap.put("x1", "终点横坐标");   // 正确的字符串配置
        lineMap.put("y1", "终点纵坐标");   // 正确的字符串配置
        template.setLineMap(lineMap);

        try {
            java.lang.reflect.Method getIndexMethod = ExcelDataListener.class
                .getDeclaredMethod("getCoordinateColumnIndex", Map.class, String.class);
            getIndexMethod.setAccessible(true);

            // null值应该抛出异常
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                try {
                    getIndexMethod.invoke(listener, lineMap, "y");
                } catch (Exception e) {
                    if (e.getCause() instanceof RuntimeException) {
                        throw (RuntimeException) e.getCause();
                    }
                    throw new RuntimeException(e);
                }
            });

            assertTrue(exception.getMessage().contains("配置值不能为空"));
            assertTrue(exception.getMessage().contains("请使用列名字符串配置"));

        } catch (Exception e) {
            fail("null配置异常测试失败: " + e.getMessage());
        }
    }
}
