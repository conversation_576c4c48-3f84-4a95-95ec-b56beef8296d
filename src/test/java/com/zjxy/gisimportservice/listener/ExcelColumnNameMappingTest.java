package com.zjxy.gisimportservice.listener;

import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.service.DataValidationService;
import com.zjxy.gisimportservice.service.Impl.CoordinateTransformService;
import com.zjxy.gisimportservice.service.Impl.HighPerformanceBatchInsertService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Excel基于列名映射功能测试
 */
public class ExcelColumnNameMappingTest {

    @Mock
    private DataValidationService validationService;

    @Mock
    private CoordinateTransformService coordinateService;

    @Mock
    private HighPerformanceBatchInsertService batchInsertService;

    private GisManageTemplate template;
    private ExcelDataListener listener;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 创建Excel模板配置
        template = new GisManageTemplate();
        template.setTemplateType("excel");
        template.setType(1); // 纯文本
        template.setExcelDataStartRow(2); // 数据从第2行开始，第1行是表头
        
        // 设置基于列名的字段映射配置
        List<Map<String, Object>> fieldMappings = new ArrayList<>();
        
        // 管道编号字段
        Map<String, Object> pipeCodeMapping = new HashMap<>();
        pipeCodeMapping.put("checked", true);
        pipeCodeMapping.put("position", 0); // 向后兼容
        pipeCodeMapping.put("columnName", "管道编号");
        pipeCodeMapping.put("fieldName", "pipe_code");
        pipeCodeMapping.put("fieldType", "string");
        fieldMappings.add(pipeCodeMapping);
        
        // 材质字段
        Map<String, Object> materialMapping = new HashMap<>();
        materialMapping.put("checked", true);
        materialMapping.put("position", 1); // 向后兼容
        materialMapping.put("columnName", "材质");
        materialMapping.put("fieldName", "material");
        materialMapping.put("fieldType", "string");
        fieldMappings.add(materialMapping);
        
        // 管径字段
        Map<String, Object> diameterMapping = new HashMap<>();
        diameterMapping.put("checked", true);
        diameterMapping.put("position", 2); // 向后兼容
        diameterMapping.put("columnName", "管径");
        diameterMapping.put("fieldName", "diameter");
        diameterMapping.put("fieldType", "integer");
        fieldMappings.add(diameterMapping);
        
        template.setMap(fieldMappings);
        
        // 创建监听器
        listener = new ExcelDataListener(template, validationService, coordinateService, 
                                       batchInsertService, 1000, "import");
    }

    @Test
    void testHeaderReading() {
        // 准备表头数据
        Map<Integer, Object> headerData = new HashMap<>();
        headerData.put(0, "管道编号");
        headerData.put(1, "材质");
        headerData.put(2, "管径");
        headerData.put(3, "长度");
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = ExcelDataListener.class
                .getDeclaredMethod("readHeader", Map.class);
            method.setAccessible(true);
            
            method.invoke(listener, headerData);
            
            // 验证表头映射是否正确建立
            java.lang.reflect.Field field = ExcelDataListener.class
                .getDeclaredField("headerMapping");
            field.setAccessible(true);
            
            @SuppressWarnings("unchecked")
            Map<String, Integer> headerMapping = (Map<String, Integer>) field.get(listener);
            
            assertEquals(4, headerMapping.size());
            assertEquals(Integer.valueOf(0), headerMapping.get("管道编号"));
            assertEquals(Integer.valueOf(1), headerMapping.get("材质"));
            assertEquals(Integer.valueOf(2), headerMapping.get("管径"));
            assertEquals(Integer.valueOf(3), headerMapping.get("长度"));
            
        } catch (Exception e) {
            fail("表头读取测试失败: " + e.getMessage());
        }
    }

    @Test
    void testColumnNameMapping() {
        // 先设置表头映射
        Map<Integer, Object> headerData = new HashMap<>();
        headerData.put(0, "管道编号");
        headerData.put(1, "材质");
        headerData.put(2, "管径");
        
        // 准备行数据（注意：列的顺序可能与position不同）
        Map<Integer, Object> rowData = new HashMap<>();
        rowData.put(0, "PIPE001");
        rowData.put(1, "PE");
        rowData.put(2, "200");
        
        try {
            // 先读取表头
            java.lang.reflect.Method readHeaderMethod = ExcelDataListener.class
                .getDeclaredMethod("readHeader", Map.class);
            readHeaderMethod.setAccessible(true);
            readHeaderMethod.invoke(listener, headerData);
            
            // 然后转换数据
            java.lang.reflect.Method convertMethod = ExcelDataListener.class
                .getDeclaredMethod("convertToGeoFeatureEntity", Map.class, int.class);
            convertMethod.setAccessible(true);
            
            GeoFeatureEntity entity = (GeoFeatureEntity) convertMethod.invoke(listener, rowData, 2);
            
            // 验证映射结果
            assertNotNull(entity);
            assertEquals("PIPE001", entity.getAttribute("pipe_code"));
            assertEquals("PE", entity.getAttribute("material"));
            assertEquals(200, entity.getAttribute("diameter"));
            
            // 验证col_x格式的字段也被正确设置
            assertEquals("PIPE001", entity.getAttribute("col_0"));
            assertEquals("PE", entity.getAttribute("col_1"));
            assertEquals(200, entity.getAttribute("col_2"));
            
        } catch (Exception e) {
            fail("列名映射测试失败: " + e.getMessage());
        }
    }

    @Test
    void testMissingColumnValidation() {
        // 设置一个缺少列的表头
        Map<Integer, Object> headerData = new HashMap<>();
        headerData.put(0, "管道编号");
        headerData.put(1, "材质");
        // 缺少"管径"列
        
        try {
            java.lang.reflect.Method method = ExcelDataListener.class
                .getDeclaredMethod("readHeader", Map.class);
            method.setAccessible(true);
            
            // 应该抛出异常，因为缺少"管径"列
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                try {
                    method.invoke(listener, headerData);
                } catch (Exception e) {
                    if (e.getCause() instanceof RuntimeException) {
                        throw (RuntimeException) e.getCause();
                    }
                    throw new RuntimeException(e);
                }
            });
            
            assertTrue(exception.getMessage().contains("Excel表中未找到以下列名"));
            assertTrue(exception.getMessage().contains("管径"));
            
        } catch (Exception e) {
            fail("缺失列验证测试失败: " + e.getMessage());
        }
    }

    @Test
    void testBackwardCompatibilityWithPosition() {
        // 创建只有position配置的模板（向后兼容测试）
        List<Map<String, Object>> fieldMappings = new ArrayList<>();
        
        Map<String, Object> mapping = new HashMap<>();
        mapping.put("checked", true);
        mapping.put("position", 0);
        mapping.put("fieldName", "pipe_code");
        mapping.put("fieldType", "string");
        // 没有columnName配置
        fieldMappings.add(mapping);
        
        template.setMap(fieldMappings);
        
        // 准备行数据
        Map<Integer, Object> rowData = new HashMap<>();
        rowData.put(0, "PIPE001");
        
        try {
            java.lang.reflect.Method convertMethod = ExcelDataListener.class
                .getDeclaredMethod("convertToGeoFeatureEntity", Map.class, int.class);
            convertMethod.setAccessible(true);
            
            GeoFeatureEntity entity = (GeoFeatureEntity) convertMethod.invoke(listener, rowData, 1);
            
            // 验证向后兼容性
            assertNotNull(entity);
            assertEquals("PIPE001", entity.getAttribute("pipe_code"));
            assertEquals("PIPE001", entity.getAttribute("col_0"));
            
        } catch (Exception e) {
            fail("向后兼容性测试失败: " + e.getMessage());
        }
    }

    @Test
    void testColumnNamePriorityOverPosition() {
        // 测试当同时存在columnName和position时，优先使用columnName
        
        // 设置表头：管道编号在第1列，而不是第0列
        Map<Integer, Object> headerData = new HashMap<>();
        headerData.put(0, "序号");
        headerData.put(1, "管道编号");
        
        // 设置字段映射：position=0，但columnName="管道编号"
        List<Map<String, Object>> fieldMappings = new ArrayList<>();
        Map<String, Object> mapping = new HashMap<>();
        mapping.put("checked", true);
        mapping.put("position", 0); // 指向"序号"列
        mapping.put("columnName", "管道编号"); // 指向第1列
        mapping.put("fieldName", "pipe_code");
        mapping.put("fieldType", "string");
        fieldMappings.add(mapping);
        
        template.setMap(fieldMappings);
        
        // 准备行数据
        Map<Integer, Object> rowData = new HashMap<>();
        rowData.put(0, "1"); // 序号
        rowData.put(1, "PIPE001"); // 管道编号
        
        try {
            // 先读取表头
            java.lang.reflect.Method readHeaderMethod = ExcelDataListener.class
                .getDeclaredMethod("readHeader", Map.class);
            readHeaderMethod.setAccessible(true);
            readHeaderMethod.invoke(listener, headerData);
            
            // 转换数据
            java.lang.reflect.Method convertMethod = ExcelDataListener.class
                .getDeclaredMethod("convertToGeoFeatureEntity", Map.class, int.class);
            convertMethod.setAccessible(true);
            
            GeoFeatureEntity entity = (GeoFeatureEntity) convertMethod.invoke(listener, rowData, 2);
            
            // 验证优先使用columnName映射，应该得到"PIPE001"而不是"1"
            assertNotNull(entity);
            assertEquals("PIPE001", entity.getAttribute("pipe_code"));
            assertEquals("PIPE001", entity.getAttribute("col_1")); // 使用列索引1
            
        } catch (Exception e) {
            fail("列名优先级测试失败: " + e.getMessage());
        }
    }
}
