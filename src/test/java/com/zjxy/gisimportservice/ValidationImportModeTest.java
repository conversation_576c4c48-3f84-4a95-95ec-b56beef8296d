package com.zjxy.gisimportservice;

import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.ValidationResult;
import com.zjxy.gisimportservice.service.TemplateBasedShapefileService;
import com.zjxy.gisimportservice.service.ExcelImportService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 验证-导入两阶段模式测试
 * 
 * 测试目标：
 * 1. 验证模式(target="valid")：只验证，不插入数据库
 * 2. 导入模式(target="import")：跳过验证，直接导入
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class ValidationImportModeTest {

    @Autowired
    private TemplateBasedShapefileService shapefileService;

    @Autowired
    private ExcelImportService excelImportService;

    /**
     * 测试Shapefile验证模式
     * 验证：只验证，不插入数据库
     */
    @Test
    public void testShapefileValidationMode() {
        log.info("=== 测试Shapefile验证模式 ===");
        
        // 准备测试数据
        String testFilePath = "test-data/test-shapefile.zip";
        GisManageTemplate template = createTestTemplate();
        String target = "valid";
        String createdBy = "test-user";
        
        // 执行验证
        ValidationResult result = shapefileService.validateShapefileData(testFilePath, template, target, createdBy);
        
        // 验证结果
        assertNotNull(result, "验证结果不应为空");
        log.info("验证结果: 总记录={}, 有效={}, 错误={}, 通过={}", 
                result.getTotalRecords(), result.getValidRecords(), result.getErrorRecords(), result.isPassed());
        
        // 验证模式应该有记录统计，但不应该插入数据库
        assertTrue(result.getTotalRecords() >= 0, "总记录数应该大于等于0");
        
        log.info("=== Shapefile验证模式测试完成 ===");
    }

    /**
     * 测试Shapefile导入模式
     * 验证：跳过验证，直接导入
     */
    @Test
    public void testShapefileImportMode() {
        log.info("=== 测试Shapefile导入模式 ===");
        
        // 准备测试数据
        String testFilePath = "test-data/test-shapefile.zip";
        GisManageTemplate template = createTestTemplate();
        String target = "import";
        String createdBy = "test-user";
        
        // 执行导入
        ValidationResult result = shapefileService.validateShapefileData(testFilePath, template, target, createdBy);
        
        // 验证结果
        assertNotNull(result, "导入结果不应为空");
        log.info("导入结果: 总记录={}, 有效={}, 错误={}, 通过={}", 
                result.getTotalRecords(), result.getValidRecords(), result.getErrorRecords(), result.isPassed());
        
        // 导入模式应该有记录统计
        assertTrue(result.getTotalRecords() >= 0, "总记录数应该大于等于0");
        
        log.info("=== Shapefile导入模式测试完成 ===");
    }

    /**
     * 测试Excel验证模式
     * 验证：只验证，不插入数据库
     */
    @Test
    public void testExcelValidationMode() {
        log.info("=== 测试Excel验证模式 ===");
        
        // 准备测试数据
        String testFilePath = "test-data/test-excel.xlsx";
        GisManageTemplate template = createTestTemplate();
        String target = "valid";
        
        // 执行验证
        ValidationResult result = excelImportService.validateExcelData(testFilePath, template, target);
        
        // 验证结果
        assertNotNull(result, "验证结果不应为空");
        log.info("验证结果: 总记录={}, 有效={}, 错误={}, 通过={}", 
                result.getTotalRecords(), result.getValidRecords(), result.getErrorRecords(), result.isPassed());
        
        // 验证模式应该有记录统计，但不应该插入数据库
        assertTrue(result.getTotalRecords() >= 0, "总记录数应该大于等于0");
        
        log.info("=== Excel验证模式测试完成 ===");
    }

    /**
     * 测试Excel导入模式
     * 验证：跳过验证，直接导入
     */
    @Test
    public void testExcelImportMode() {
        log.info("=== 测试Excel导入模式 ===");
        
        // 准备测试数据
        String testFilePath = "test-data/test-excel.xlsx";
        GisManageTemplate template = createTestTemplate();
        String target = "import";
        
        // 执行导入
        ValidationResult result = excelImportService.validateExcelData(testFilePath, template, target);
        
        // 验证结果
        assertNotNull(result, "导入结果不应为空");
        log.info("导入结果: 总记录={}, 有效={}, 错误={}, 通过={}", 
                result.getTotalRecords(), result.getValidRecords(), result.getErrorRecords(), result.isPassed());
        
        // 导入模式应该有记录统计
        assertTrue(result.getTotalRecords() >= 0, "总记录数应该大于等于0");
        
        log.info("=== Excel导入模式测试完成 ===");
    }

    /**
     * 测试不支持的目标模式
     */
    @Test
    public void testUnsupportedTargetMode() {
        log.info("=== 测试不支持的目标模式 ===");
        
        // 准备测试数据
        String testFilePath = "test-data/test-shapefile.zip";
        GisManageTemplate template = createTestTemplate();
        String target = "unsupported";
        String createdBy = "test-user";
        
        // 执行验证
        ValidationResult result = shapefileService.validateShapefileData(testFilePath, template, target, createdBy);
        
        // 验证结果
        assertNotNull(result, "结果不应为空");
        assertFalse(result.isPassed(), "不支持的模式应该返回失败");
        assertTrue(result.getMessage().contains("不支持的目标模式"), "错误消息应该包含不支持的目标模式");
        
        log.info("=== 不支持的目标模式测试完成 ===");
    }

    /**
     * 创建测试模板
     */
    private GisManageTemplate createTestTemplate() {
        GisManageTemplate template = new GisManageTemplate();
        template.setId(1);
        template.setNameZh("测试模板");
        template.setTableName("test_table");
        template.setDataBase("test_db");
        template.setTemplateType("shapefile");
        return template;
    }

    /**
     * 性能对比测试
     * 比较验证模式和导入模式的性能差异
     */
    @Test
    public void testPerformanceComparison() {
        log.info("=== 性能对比测试 ===");
        
        String testFilePath = "test-data/test-shapefile.zip";
        GisManageTemplate template = createTestTemplate();
        String createdBy = "test-user";
        
        // 测试验证模式性能
        long validationStartTime = System.currentTimeMillis();
        ValidationResult validationResult = shapefileService.validateShapefileData(testFilePath, template, "valid", createdBy);
        long validationEndTime = System.currentTimeMillis();
        long validationDuration = validationEndTime - validationStartTime;
        
        // 测试导入模式性能
        long importStartTime = System.currentTimeMillis();
        ValidationResult importResult = shapefileService.validateShapefileData(testFilePath, template, "import", createdBy);
        long importEndTime = System.currentTimeMillis();
        long importDuration = importEndTime - importStartTime;
        
        log.info("性能对比结果:");
        log.info("验证模式耗时: {}ms", validationDuration);
        log.info("导入模式耗时: {}ms", importDuration);
        
        if (validationDuration > 0 && importDuration > 0) {
            double performanceRatio = (double) validationDuration / importDuration;
            log.info("验证模式相对导入模式的性能比: {:.2f}", performanceRatio);
            
            // 验证模式应该比导入模式快（因为不执行数据库插入）
            // 但这个断言可能因为测试环境而不稳定，所以只记录日志
            if (performanceRatio < 1.0) {
                log.info("✓ 验证模式比导入模式快 {:.1f}%", (1 - performanceRatio) * 100);
            } else {
                log.warn("⚠ 验证模式比导入模式慢，可能需要优化");
            }
        }
        
        log.info("=== 性能对比测试完成 ===");
    }
}
