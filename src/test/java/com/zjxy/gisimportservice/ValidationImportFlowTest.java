package com.zjxy.gisimportservice;

import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.ValidationResult;
import com.zjxy.gisimportservice.service.GisImportTaskService;
import com.zjxy.gisimportservice.service.GisManageTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 验证-导入两阶段流程完整测试
 * 
 * 测试完整的业务流程：
 * 1. 创建任务 (data_status=1)
 * 2. 执行验证 (data_status=1→0)
 * 3. 执行导入 (data_status=0→2)
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class ValidationImportFlowTest {

    @Autowired
    private GisImportTaskService gisImportTaskService;

    @Autowired
    private GisManageTemplateService templateService;

    /**
     * 测试完整的Shapefile验证-导入流程
     */
    @Test
    public void testCompleteShapefileFlow() {
        log.info("=== 开始测试Shapefile完整流程 ===");
        
        try {
            // 步骤1：创建任务（模拟文件上传）
            GisImportTask task = createTestShapefileTask();
            log.info("步骤1：创建任务完成 - 任务ID: {}, 初始状态: {}", 
                    task.getId(), task.getDataStatus().getDescription());
            
            // 验证初始状态
            assertEquals(GisImportTask.DataStatus.DATA_UNCHECKED, task.getDataStatus(), 
                    "初始状态应该是数据未检查");
            assertTrue(task.canValidate(), "初始状态应该允许验证");
            assertFalse(task.canImport(), "初始状态不应该允许导入");
            
            // 步骤2：执行验证
            log.info("步骤2：开始执行验证...");
            ValidationResult validationResult = gisImportTaskService.validateShapefileData(task);
            log.info("验证结果: 通过={}, 总记录={}, 有效={}, 错误={}", 
                    validationResult.isPassed(), validationResult.getTotalRecords(), 
                    validationResult.getValidRecords(), validationResult.getErrorRecords());
            
            // 重新获取任务状态
            task = gisImportTaskService.getById(task.getId());
            log.info("验证后任务状态: {}", task.getDataStatus().getDescription());
            
            if (validationResult.isPassed()) {
                // 验证成功的情况
                assertEquals(GisImportTask.DataStatus.NOT_IMPORTED, task.getDataStatus(), 
                        "验证成功后状态应该是数据未导入");
                assertFalse(task.canValidate(), "验证完成后不应该允许再次验证（除非重新验证）");
                assertTrue(task.canImport(), "验证成功后应该允许导入");
                
                // 步骤3：执行导入
                log.info("步骤3：开始执行导入...");
                var importResult = gisImportTaskService.executeShapefileImport(task);
                log.info("导入结果: 成功={}, 导入记录={}", 
                        importResult.get("success"), importResult.get("importedRecords"));
                
                // 重新获取任务状态
                task = gisImportTaskService.getById(task.getId());
                log.info("导入后任务状态: {}", task.getDataStatus().getDescription());
                
                if ((Boolean) importResult.get("success")) {
                    assertEquals(GisImportTask.DataStatus.DATA_IMPORTED, task.getDataStatus(), 
                            "导入成功后状态应该是数据已导入");
                    log.info("✓ 完整流程测试成功：1(未检查) → 0(未导入) → 2(已导入)");
                } else {
                    assertEquals(GisImportTask.DataStatus.NOT_IMPORTED, task.getDataStatus(), 
                            "导入失败后状态应该保持为数据未导入");
                    log.warn("⚠ 导入失败，状态保持为未导入");
                }
            } else {
                // 验证失败的情况
                assertEquals(GisImportTask.DataStatus.DATA_UNCHECKED, task.getDataStatus(), 
                        "验证失败后状态应该保持为数据未检查");
                assertTrue(task.canValidate(), "验证失败后应该允许重新验证");
                assertFalse(task.canImport(), "验证失败后不应该允许导入");
                log.warn("⚠ 验证失败，状态保持为未检查");
            }
            
        } catch (Exception e) {
            log.error("测试过程中发生异常", e);
            fail("测试失败: " + e.getMessage());
        }
        
        log.info("=== Shapefile完整流程测试结束 ===");
    }

    /**
     * 测试完整的Excel验证-导入流程
     */
    @Test
    public void testCompleteExcelFlow() {
        log.info("=== 开始测试Excel完整流程 ===");
        
        try {
            // 步骤1：创建任务（模拟文件上传）
            GisImportTask task = createTestExcelTask();
            log.info("步骤1：创建任务完成 - 任务ID: {}, 初始状态: {}", 
                    task.getId(), task.getDataStatus().getDescription());
            
            // 验证初始状态
            assertEquals(GisImportTask.DataStatus.DATA_UNCHECKED, task.getDataStatus(), 
                    "初始状态应该是数据未检查");
            
            // 步骤2：执行验证
            log.info("步骤2：开始执行验证...");
            ValidationResult validationResult = gisImportTaskService.validateExcelData(task);
            log.info("验证结果: 通过={}, 总记录={}, 有效={}, 错误={}", 
                    validationResult.isPassed(), validationResult.getTotalRecords(), 
                    validationResult.getValidRecords(), validationResult.getErrorRecords());
            
            // 重新获取任务状态
            task = gisImportTaskService.getById(task.getId());
            log.info("验证后任务状态: {}", task.getDataStatus().getDescription());
            
            if (validationResult.isPassed()) {
                // 验证成功，执行导入
                assertEquals(GisImportTask.DataStatus.NOT_IMPORTED, task.getDataStatus(), 
                        "验证成功后状态应该是数据未导入");
                
                // 步骤3：执行导入
                log.info("步骤3：开始执行导入...");
                var importResult = gisImportTaskService.executeExcelImport(task);
                log.info("导入结果: 成功={}, 导入记录={}", 
                        importResult.get("success"), importResult.get("importedRecords"));
                
                // 重新获取任务状态
                task = gisImportTaskService.getById(task.getId());
                log.info("导入后任务状态: {}", task.getDataStatus().getDescription());
                
                if ((Boolean) importResult.get("success")) {
                    assertEquals(GisImportTask.DataStatus.DATA_IMPORTED, task.getDataStatus(), 
                            "导入成功后状态应该是数据已导入");
                    log.info("✓ Excel完整流程测试成功：1(未检查) → 0(未导入) → 2(已导入)");
                }
            } else {
                // 验证失败
                assertEquals(GisImportTask.DataStatus.DATA_UNCHECKED, task.getDataStatus(), 
                        "验证失败后状态应该保持为数据未检查");
                log.warn("⚠ Excel验证失败，状态保持为未检查");
            }
            
        } catch (Exception e) {
            log.error("Excel测试过程中发生异常", e);
            fail("Excel测试失败: " + e.getMessage());
        }
        
        log.info("=== Excel完整流程测试结束 ===");
    }

    /**
     * 测试状态转换的边界条件
     */
    @Test
    public void testStatusTransitionBoundaries() {
        log.info("=== 测试状态转换边界条件 ===");
        
        try {
            // 创建任务
            GisImportTask task = createTestShapefileTask();
            
            // 测试1：在未验证状态下尝试导入（应该失败）
            try {
                gisImportTaskService.executeShapefileImport(task);
                fail("在未验证状态下应该不能执行导入");
            } catch (IllegalStateException e) {
                log.info("✓ 正确阻止了未验证状态下的导入操作: {}", e.getMessage());
            }
            
            // 测试2：验证成功后的状态
            ValidationResult validationResult = gisImportTaskService.validateShapefileData(task);
            task = gisImportTaskService.getById(task.getId());
            
            if (validationResult.isPassed()) {
                // 测试3：在已验证状态下尝试再次验证（应该失败）
                try {
                    gisImportTaskService.validateShapefileData(task);
                    fail("在已验证状态下应该不能再次验证");
                } catch (IllegalStateException e) {
                    log.info("✓ 正确阻止了已验证状态下的重复验证: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("边界条件测试失败", e);
            fail("边界条件测试失败: " + e.getMessage());
        }
        
        log.info("=== 状态转换边界条件测试结束 ===");
    }

    /**
     * 创建测试用的Shapefile任务
     */
    private GisImportTask createTestShapefileTask() {
        GisImportTask task = new GisImportTask();
        task.setTaskName("测试Shapefile任务");
        task.setFilePath("/test/data/test-shapefile.zip");
        task.setTemplateId(1);
        task.setImportFormat(GisImportTask.ImportFormat.SHP);
        task.setDataStatus(GisImportTask.DataStatus.DATA_UNCHECKED);
        task.setCreatedBy("test-user");
        task.setFileSize(1024L);
        
        // 保存任务
        gisImportTaskService.save(task);
        return task;
    }

    /**
     * 创建测试用的Excel任务
     */
    private GisImportTask createTestExcelTask() {
        GisImportTask task = new GisImportTask();
        task.setTaskName("测试Excel任务");
        task.setFilePath("/test/data/test-excel.xlsx");
        task.setTemplateId(1);
        task.setImportFormat(GisImportTask.ImportFormat.EXCEL);
        task.setDataStatus(GisImportTask.DataStatus.DATA_UNCHECKED);
        task.setCreatedBy("test-user");
        task.setFileSize(2048L);
        
        // 保存任务
        gisImportTaskService.save(task);
        return task;
    }

    /**
     * 性能对比测试
     */
    @Test
    public void testPerformanceComparison() {
        log.info("=== 性能对比测试 ===");
        
        try {
            GisImportTask task = createTestShapefileTask();
            
            // 测试验证性能
            long validationStart = System.currentTimeMillis();
            ValidationResult validationResult = gisImportTaskService.validateShapefileData(task);
            long validationEnd = System.currentTimeMillis();
            long validationDuration = validationEnd - validationStart;
            
            log.info("验证耗时: {}ms", validationDuration);
            
            if (validationResult.isPassed()) {
                // 测试导入性能
                long importStart = System.currentTimeMillis();
                var importResult = gisImportTaskService.executeShapefileImport(task);
                long importEnd = System.currentTimeMillis();
                long importDuration = importEnd - importStart;
                
                log.info("导入耗时: {}ms", importDuration);
                log.info("性能比较: 验证/导入 = {:.2f}", (double) validationDuration / importDuration);
            }
            
        } catch (Exception e) {
            log.warn("性能测试过程中发生异常（这是正常的，因为可能没有实际的测试文件）: {}", e.getMessage());
        }
        
        log.info("=== 性能对比测试结束 ===");
    }
}
